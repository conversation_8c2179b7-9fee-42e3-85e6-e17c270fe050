# Server-Side Rendering (SSR) vs Static Site Generation (SSG)

Understanding the different rendering strategies in Next.js is crucial for building performant and SEO-friendly applications. This guide covers SSR, SSG, ISR, and when to use each approach.

## Rendering Strategies Overview

### Client-Side Rendering (CSR)
Traditional React apps render on the client side. The server sends a minimal HTML file, and JavaScript builds the page in the browser.

**Pros:**
- Rich interactivity
- Reduced server load
- Good for authenticated content

**Cons:**
- Poor initial SEO
- Slower initial page load
- Requires JavaScript to function

### Server-Side Rendering (SSR)
Pages are rendered on the server for each request, sending fully formed HTML to the client.

**Pros:**
- Excellent SEO
- Fast initial page load
- Works without JavaScript
- Always fresh data

**Cons:**
- Higher server load
- Slower navigation between pages
- More complex caching

### Static Site Generation (SSG)
Pages are pre-rendered at build time and served as static files.

**Pros:**
- Fastest possible loading
- Excellent SEO
- Highly cacheable
- Low server costs

**Cons:**
- Data can become stale
- Longer build times
- Not suitable for dynamic content

### Incremental Static Regeneration (ISR)
Combines SSG with the ability to update static content without rebuilding the entire site.

**Pros:**
- Fast loading like SSG
- Can update stale content
- Reduced build times
- Good for semi-dynamic content

**Cons:**
- More complex setup
- Potential for serving stale data
- Cache invalidation complexity

## Static Site Generation (SSG)

### Basic SSG with getStaticProps

```jsx
// pages/products.js
export default function Products({ products }) {
    return (
        <div>
            <h1>Our Products</h1>
            <div className="products-grid">
                {products.map(product => (
                    <div key={product.id} className="product-card">
                        <h2>{product.name}</h2>
                        <p>{product.description}</p>
                        <span>${product.price}</span>
                    </div>
                ))}
            </div>
        </div>
    );
}

// This function runs at build time
export async function getStaticProps() {
    // Fetch data from API, database, or file system
    const res = await fetch('https://api.example.com/products');
    const products = await res.json();
    
    return {
        props: {
            products,
        },
        // Optional: regenerate the page at most once every hour
        revalidate: 3600, // ISR - revalidate every hour
    };
}
```

### Dynamic SSG with getStaticPaths

```jsx
// pages/products/[id].js
export default function Product({ product }) {
    if (!product) {
        return <div>Product not found</div>;
    }
    
    return (
        <div>
            <h1>{product.name}</h1>
            <img src={product.image} alt={product.name} />
            <p>{product.description}</p>
            <p>Price: ${product.price}</p>
            <p>In Stock: {product.stock}</p>
        </div>
    );
}

// Generate static paths at build time
export async function getStaticPaths() {
    // Fetch all product IDs
    const res = await fetch('https://api.example.com/products');
    const products = await res.json();
    
    // Generate paths for each product
    const paths = products.map(product => ({
        params: { id: product.id.toString() }
    }));
    
    return {
        paths,
        fallback: 'blocking', // or false, true, or 'blocking'
    };
}

export async function getStaticProps({ params }) {
    try {
        const res = await fetch(`https://api.example.com/products/${params.id}`);
        
        if (!res.ok) {
            return {
                notFound: true,
            };
        }
        
        const product = await res.json();
        
        return {
            props: {
                product,
            },
            revalidate: 3600, // ISR
        };
    } catch (error) {
        return {
            notFound: true,
        };
    }
}
```

### Fallback Options

```jsx
export async function getStaticPaths() {
    // Pre-generate only the most popular products
    const popularProducts = await fetchPopularProducts();
    
    const paths = popularProducts.map(product => ({
        params: { id: product.id.toString() }
    }));
    
    return {
        paths,
        fallback: 'blocking', // Options: false, true, 'blocking'
    };
}

// fallback: false
// - Only pre-generated paths are valid
// - 404 for any other path

// fallback: true
// - Shows loading state while generating
// - Good for large number of pages
// - Requires handling loading state in component

// fallback: 'blocking'
// - Waits for generation before showing page
// - No loading state needed
// - Better for SEO
```

## Server-Side Rendering (SSR)

### Basic SSR with getServerSideProps

```jsx
// pages/dashboard.js
export default function Dashboard({ user, notifications }) {
    return (
        <div>
            <h1>Welcome, {user.name}!</h1>
            <div className="notifications">
                <h2>Recent Notifications</h2>
                {notifications.map(notification => (
                    <div key={notification.id} className="notification">
                        <p>{notification.message}</p>
                        <small>{notification.timestamp}</small>
                    </div>
                ))}
            </div>
        </div>
    );
}

// This function runs on every request
export async function getServerSideProps(context) {
    const { req, res, query, params } = context;
    
    // Access cookies, headers, etc.
    const token = req.cookies.authToken;
    
    if (!token) {
        return {
            redirect: {
                destination: '/login',
                permanent: false,
            },
        };
    }
    
    try {
        // Fetch user-specific data
        const [userRes, notificationsRes] = await Promise.all([
            fetch('https://api.example.com/user', {
                headers: { Authorization: `Bearer ${token}` }
            }),
            fetch('https://api.example.com/notifications', {
                headers: { Authorization: `Bearer ${token}` }
            })
        ]);
        
        const user = await userRes.json();
        const notifications = await notificationsRes.json();
        
        return {
            props: {
                user,
                notifications,
            },
        };
    } catch (error) {
        return {
            redirect: {
                destination: '/error',
                permanent: false,
            },
        };
    }
}
```

### SSR with Error Handling

```jsx
export async function getServerSideProps(context) {
    try {
        const data = await fetchData();
        
        return {
            props: { data },
        };
    } catch (error) {
        console.error('SSR Error:', error);
        
        // Return error props instead of crashing
        return {
            props: {
                error: 'Failed to load data',
                data: null,
            },
        };
    }
}

export default function Page({ data, error }) {
    if (error) {
        return <ErrorComponent message={error} />;
    }
    
    return <DataComponent data={data} />;
}
```

## Incremental Static Regeneration (ISR)

### Basic ISR Implementation

```jsx
// pages/blog/[slug].js
export default function BlogPost({ post, lastUpdated }) {
    return (
        <article>
            <h1>{post.title}</h1>
            <p>Published: {post.publishedAt}</p>
            <p>Last updated: {lastUpdated}</p>
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
        </article>
    );
}

export async function getStaticPaths() {
    // Pre-generate popular posts
    const popularPosts = await fetchPopularPosts();
    
    const paths = popularPosts.map(post => ({
        params: { slug: post.slug }
    }));
    
    return {
        paths,
        fallback: 'blocking',
    };
}

export async function getStaticProps({ params }) {
    const post = await fetchPost(params.slug);
    
    if (!post) {
        return {
            notFound: true,
        };
    }
    
    return {
        props: {
            post,
            lastUpdated: new Date().toISOString(),
        },
        // Regenerate the page at most once every 60 seconds
        revalidate: 60,
    };
}
```

### On-Demand ISR (Next.js 12.2+)

```jsx
// pages/api/revalidate.js
export default async function handler(req, res) {
    // Check for secret to confirm this is a valid request
    if (req.query.secret !== process.env.REVALIDATION_SECRET) {
        return res.status(401).json({ message: 'Invalid token' });
    }
    
    try {
        // Revalidate specific paths
        await res.revalidate('/blog');
        await res.revalidate(`/blog/${req.query.slug}`);
        
        return res.json({ revalidated: true });
    } catch (err) {
        return res.status(500).send('Error revalidating');
    }
}

// Trigger revalidation from your CMS webhook
// POST /api/revalidate?secret=YOUR_SECRET&slug=post-slug
```

## Choosing the Right Strategy

### Decision Matrix

| Content Type | Frequency of Updates | User-Specific | Recommended Strategy |
|--------------|---------------------|---------------|---------------------|
| Marketing pages | Rarely | No | SSG |
| Blog posts | Occasionally | No | SSG with ISR |
| Product catalog | Daily | No | SSG with ISR |
| User dashboard | Real-time | Yes | SSR |
| Search results | Real-time | Varies | SSR |
| Comments | Real-time | No | CSR + API |

### Use SSG When:
- Content doesn't change frequently
- Same content for all users
- SEO is important
- Performance is critical
- You can predict the pages needed

### Use SSR When:
- Content changes frequently
- Content is user-specific
- You need real-time data
- Pages can't be pre-generated
- Authentication is required

### Use ISR When:
- Content changes occasionally
- You want SSG benefits with some freshness
- You have many pages to generate
- Build times are becoming too long
- You can tolerate slightly stale data

## Performance Considerations

### Optimizing SSG Build Times

```jsx
// Limit the number of pages generated at build time
export async function getStaticPaths() {
    // Only pre-generate the most important pages
    const importantPosts = await fetchImportantPosts(100); // Limit to 100
    
    const paths = importantPosts.map(post => ({
        params: { slug: post.slug }
    }));
    
    return {
        paths,
        fallback: 'blocking', // Generate others on-demand
    };
}
```

### Caching Strategies

```jsx
export async function getServerSideProps(context) {
    // Set cache headers
    context.res.setHeader(
        'Cache-Control',
        'public, s-maxage=10, stale-while-revalidate=59'
    );
    
    const data = await fetchData();
    
    return {
        props: { data },
    };
}
```

### Parallel Data Fetching

```jsx
export async function getStaticProps() {
    // Fetch data in parallel
    const [posts, categories, tags] = await Promise.all([
        fetchPosts(),
        fetchCategories(),
        fetchTags(),
    ]);
    
    return {
        props: {
            posts,
            categories,
            tags,
        },
        revalidate: 3600,
    };
}
```

## Best Practices

1. **Start with SSG** and move to SSR only when necessary
2. **Use ISR for semi-dynamic content** that doesn't need real-time updates
3. **Implement proper error handling** in data fetching functions
4. **Cache API responses** to improve build times and reduce API calls
5. **Use fallback: 'blocking'** for better SEO than fallback: true
6. **Monitor your build times** and optimize when they become too long
7. **Implement proper loading states** for fallback pages
8. **Use environment variables** for API endpoints and secrets
9. **Test your pages** in different scenarios (build time, runtime, etc.)
10. **Monitor Core Web Vitals** to ensure good performance

Understanding these rendering strategies allows you to build Next.js applications that are both performant and provide excellent user experiences while maintaining good SEO characteristics.
